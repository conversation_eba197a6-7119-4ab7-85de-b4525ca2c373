import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const VENDOR_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Enter Vendor Code',
        topGroupTitleIcon: 'pi-building',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Vendor Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Name",
        placeholder: 'Enter Vendor Name',
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact Number",
        placeholder: 'Enter Contact Number',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "E-mail",
        placeholder: 'Enter Email',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    altemail: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Alternate E-mail",
        placeholder: 'Enter Alternate E-mail',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address",
        placeholder: 'Enter Address',
        topGroupTitle: 'Address Details',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Enter City',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    state: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "State",
        placeholder: 'Enter State',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PIN Code",
        placeholder: 'Enter PIN Code',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 10,
        }
    },
    country: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [
        ],
        value: "",
        label: "Country",
        placeholder: 'Select Country',
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    technicianCode: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Technician",
        placeholder: 'Select Technician',
        topGroupTitle: 'Assignment Details',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            // Not required
        }
    }
}
